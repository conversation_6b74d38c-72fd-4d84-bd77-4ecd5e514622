@echo off
echo ========================================
echo Sentra Auto Browser - Troubleshooting
echo ========================================

echo [INFO] System Information:
echo Node.js version: 
node --version
echo npm version:
npm --version
echo Current directory: %CD%
echo.

echo [CHECK 1] Checking package.json...
if exist package.json (
    echo [OK] package.json found
) else (
    echo [ERROR] package.json not found!
    echo Please make sure you are in the correct directory.
    pause
    exit /b 1
)

echo [CHECK 2] Checking node_modules...
if exist node_modules (
    echo [OK] node_modules found
) else (
    echo [WARNING] node_modules not found
    echo Running npm install...
    call npm install --legacy-peer-deps
)

echo [CHECK 3] Checking dist folder...
if exist dist (
    echo [OK] dist folder found
) else (
    echo [WARNING] dist folder not found
    echo Running build...
    call npm run build
)

echo [CHECK 4] Checking CLI file...
if exist dist\cli\index.js (
    echo [OK] CLI file found
) else (
    echo [ERROR] CLI file not found
    echo Rebuilding project...
    call npm run build
    if errorlevel 1 (
        echo [ERROR] Build failed!
        pause
        exit /b 1
    )
)

echo [CHECK 5] Testing basic functionality...
node dist/cli/index.js --help
if errorlevel 1 (
    echo [ERROR] CLI help command failed!
    echo This indicates a serious issue with the installation.
    pause
    exit /b 1
) else (
    echo [OK] CLI help command works
)

echo [CHECK 6] Checking environment file...
if exist .env (
    echo [OK] .env file found
    echo Checking API keys...
    findstr /C:"GOOGLE_API_KEY" .env >nul
    if errorlevel 1 (
        echo [WARNING] GOOGLE_API_KEY not found in .env
    ) else (
        echo [OK] GOOGLE_API_KEY found in .env
    )
) else (
    echo [WARNING] .env file not found
    echo Creating .env from example...
    if exist .env.example (
        copy .env.example .env
        echo [INFO] Please edit .env file and add your API keys
    ) else (
        echo [ERROR] .env.example not found either!
    )
)

echo.
echo ========================================
echo Troubleshooting completed!
echo ========================================
echo.
echo If you still have issues:
echo 1. Make sure you have Node.js 18+ installed
echo 2. Check your internet connection
echo 3. Verify your API keys in .env file
echo 4. Try running: npm install --legacy-peer-deps
echo 5. Try running: npm run build
echo.
pause
