@echo off
setlocal enabledelayedexpansion

REM Check parameters
if "%~1"=="" (
    echo Usage: run.bat "task description" [--provider provider] [--model model] [other options]
    echo Example: run.bat "visit baidu and search Node.js" --provider google --model gemini-1.5-flash
    exit /b 1
)

REM Build project
echo Building project...
call npm run build
if errorlevel 1 (
    echo Build failed!
    exit /b 1
)

REM Run CLI
echo Running task...
node dist/cli/index.js %*
