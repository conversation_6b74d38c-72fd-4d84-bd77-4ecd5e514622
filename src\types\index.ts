// Core types for the browser-use-nodejs project

export interface BrowserProfile {
  headless?: boolean;
  viewport?: { width: number; height: number };
  userDataDir?: string;
  executablePath?: string;
  timeout?: number;
  slowMo?: number;
  devtools?: boolean;
  args?: string[];
  ignoreHTTPSErrors?: boolean;
  proxy?: {
    server: string;
    username?: string;
    password?: string;
  };
  locale?: string;
  timezone?: string;
  geolocation?: { latitude: number; longitude: number };
  permissions?: string[];
  extraHTTPHeaders?: Record<string, string>;
  userAgent?: string;
  colorScheme?: 'light' | 'dark' | 'no-preference';
  reducedMotion?: 'reduce' | 'no-preference';
  forcedColors?: 'active' | 'none';
}

export interface ActionResult {
  success: boolean;
  message?: string;
  error?: string;
  extractedContent?: string;
  screenshot?: string;
  metadata?: {
    duration: number;
    timestamp: Date;
    url: string;
    title: string;
    elementCount?: number;
    retryCount?: number;
  };
}

export interface AgentSettings {
  maxSteps?: number;
  maxActionsPerStep?: number;
  useVision?: boolean;
  temperature?: number;
  retryFailedActions?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableMemory?: boolean;
  memorySize?: number;
  enablePlanning?: boolean;
  planningSteps?: number;
  enableReflection?: boolean;
  reflectionInterval?: number;
  enableErrorRecovery?: boolean;
  errorRecoveryStrategies?: ErrorRecoveryStrategy[];
  enablePerformanceMonitoring?: boolean;
  enableScreenshotOnError?: boolean;
  enableActionValidation?: boolean;
  customPrompts?: {
    systemPrompt?: string;
    planningPrompt?: string;
    reflectionPrompt?: string;
    errorRecoveryPrompt?: string;
  };
}

export interface LLMConfig {
  provider: 'openai' | 'anthropic' | 'google';
  model: string;
  apiKey: string;
  temperature?: number;
}

export interface DOMElement {
  index: number;
  tag: string;
  text?: string;
  attributes: Record<string, string>;
  xpath: string;
  isClickable: boolean;
  isVisible: boolean;
}

export interface DOMState {
  elements: DOMElement[];
  url: string;
  title: string;
  screenshot?: string;
}

// Action types
export interface ClickAction {
  type: 'click';
  index: number;
  xpath?: string;
}

export interface TypeAction {
  type: 'type';
  index: number;
  text: string;
  xpath?: string;
}

export interface NavigateAction {
  type: 'navigate';
  url: string;
}

export interface ScrollAction {
  type: 'scroll';
  direction: 'up' | 'down';
  amount?: number;
}

export interface WaitAction {
  type: 'wait';
  seconds: number;
}

export interface DoneAction {
  type: 'done';
  message: string;
  success: boolean;
}

// Extended action types
export interface HoverAction {
  type: 'hover';
  index: number;
  xpath?: string;
}

export interface DragDropAction {
  type: 'drag_drop';
  sourceIndex: number;
  targetIndex: number;
  sourceXpath?: string;
  targetXpath?: string;
}

export interface KeyPressAction {
  type: 'key_press';
  key: string;
  modifiers?: ('ctrl' | 'alt' | 'shift' | 'meta')[];
}

export interface SelectAction {
  type: 'select';
  index: number;
  value: string | string[];
  xpath?: string;
}

export interface UploadFileAction {
  type: 'upload_file';
  index: number;
  filePath: string;
  xpath?: string;
}

export interface TakeScreenshotAction {
  type: 'take_screenshot';
  fullPage?: boolean;
  element?: number;
}

export interface ExtractDataAction {
  type: 'extract_data';
  selector?: string;
  xpath?: string;
  attribute?: string;
  multiple?: boolean;
}

export interface ExecuteScriptAction {
  type: 'execute_script';
  script: string;
  args?: any[];
}

export interface SwitchTabAction {
  type: 'switch_tab';
  tabIndex?: number;
  url?: string;
}

export interface NewTabAction {
  type: 'new_tab';
  url?: string;
}

export interface CloseTabAction {
  type: 'close_tab';
  tabIndex?: number;
}

export interface GoBackAction {
  type: 'go_back';
}

export interface GoForwardAction {
  type: 'go_forward';
}

export interface RefreshAction {
  type: 'refresh';
}

export interface SetCookieAction {
  type: 'set_cookie';
  name: string;
  value: string;
  domain?: string;
  path?: string;
  expires?: number;
}

export interface WaitForElementAction {
  type: 'wait_for_element';
  selector?: string;
  xpath?: string;
  timeout?: number;
  state?: 'visible' | 'hidden' | 'attached' | 'detached';
}

export interface WaitForNavigationAction {
  type: 'wait_for_navigation';
  timeout?: number;
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle';
}

export type Action =
  | ClickAction
  | TypeAction
  | NavigateAction
  | ScrollAction
  | WaitAction
  | DoneAction
  | HoverAction
  | DragDropAction
  | KeyPressAction
  | SelectAction
  | UploadFileAction
  | TakeScreenshotAction
  | ExtractDataAction
  | ExecuteScriptAction
  | SwitchTabAction
  | NewTabAction
  | CloseTabAction
  | GoBackAction
  | GoForwardAction
  | RefreshAction
  | SetCookieAction
  | WaitForElementAction
  | WaitForNavigationAction;

export interface AgentStep {
  stepNumber: number;
  action: Action;
  result: ActionResult;
  domState?: DOMState;
  timestamp: Date;
}

export interface AgentHistory {
  task: string;
  steps: AgentStep[];
  completed: boolean;
  success: boolean;
  totalDuration: number;
  startTime: Date;
  endTime?: Date;
  metadata: {
    agentId: string;
    sessionId: string;
    llmProvider: string;
    llmModel: string;
    browserProfile: Partial<BrowserProfile>;
    agentSettings: Partial<AgentSettings>;
    totalTokensUsed?: number;
    totalCost?: number;
    averageStepDuration: number;
    successRate: number;
    errorCount: number;
    retryCount: number;
    screenshotCount: number;
    finalUrl?: string;
    finalTitle?: string;
  };
  planning?: PlanningResult;
  reflection?: ReflectionResult[];
  performance?: PerformanceMetrics;
}

// New advanced types
export interface ErrorRecoveryStrategy {
  type: 'retry' | 'alternative_action' | 'skip' | 'restart_browser' | 'custom';
  maxAttempts: number;
  delay: number;
  condition?: (error: Error, context: ActionContext) => boolean;
  customHandler?: (error: Error, context: ActionContext) => Promise<ActionResult>;
}

export interface ActionContext {
  stepNumber: number;
  previousActions: Action[];
  currentDOMState: DOMState;
  agentHistory: AgentStep[];
  retryCount: number;
}

export interface PlanningResult {
  plan: PlannedStep[];
  confidence: number;
  estimatedDuration: number;
  riskAssessment: RiskAssessment;
  alternatives?: PlannedStep[][];
}

export interface PlannedStep {
  stepNumber: number;
  action: Action;
  description: string;
  expectedOutcome: string;
  confidence: number;
  dependencies?: number[];
  alternatives?: Action[];
}

export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high';
  risks: Risk[];
  mitigations: string[];
}

export interface Risk {
  type: 'element_not_found' | 'network_error' | 'timeout' | 'permission_denied' | 'rate_limit' | 'custom';
  probability: number;
  impact: 'low' | 'medium' | 'high';
  description: string;
  mitigation?: string;
}

export interface ReflectionResult {
  stepNumber: number;
  analysis: string;
  improvements: string[];
  confidence: number;
  shouldContinue: boolean;
  suggestedActions?: Action[];
}

export interface PerformanceMetrics {
  totalExecutionTime: number;
  averageActionTime: number;
  slowestAction: { action: Action; duration: number };
  fastestAction: { action: Action; duration: number };
  memoryUsage: {
    initial: number;
    peak: number;
    final: number;
  };
  networkRequests: number;
  screenshotsTaken: number;
  errorsEncountered: number;
  retriesPerformed: number;
}
