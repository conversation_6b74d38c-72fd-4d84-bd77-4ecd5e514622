# Sentra Auto Browser - 快速开始

## 安装步骤

1. **清理并安装依赖**
   ```bash
   # 删除旧的依赖（如果存在）
   rmdir /s /q node_modules
   del package-lock.json
   
   # 安装依赖
   npm install --legacy-peer-deps
   ```

2. **构建项目**
   ```bash
   npm run build
   ```

3. **配置API密钥**
   - 复制 `.env.example` 到 `.env`
   - 在 `.env` 文件中添加您的Google API密钥

## 使用方法

### 基本命令

```bash
# 查看帮助
node dist/cli/index.js --help

# 查看配置
node dist/cli/index.js config

# 测试连接
node dist/cli/index.js test

# 运行任务
node dist/cli/index.js run "访问百度首页" --provider google --model gemini-1.5-flash
```

### 使用批处理文件（Windows）

```bash
# 运行任务
run.bat "访问百度搜索Node.js" --provider google --model gemini-1.5-flash
```

## 常见任务示例

```bash
# 访问网站
node dist/cli/index.js run "访问百度首页"

# 搜索内容
node dist/cli/index.js run "访问Google搜索JavaScript"

# 复杂任务
node dist/cli/index.js run "访问淘宝，搜索手机，查看第一个商品"
```

## 故障排除

如果遇到问题：

1. **模块找不到错误**
   ```bash
   npm install --legacy-peer-deps --force
   npm run build
   ```

2. **TypeScript编译错误**
   ```bash
   npm install -g typescript@4.9.5
   npm run build
   ```

3. **API错误**
   - 检查 `.env` 文件中的API密钥
   - 确保API密钥有效且有足够配额

## 项目结构

```
sentra-auto-browser/
├── src/                 # 源代码
├── dist/                # 编译后的代码
├── .env                 # 环境配置
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript配置
└── run.bat             # Windows运行脚本
```

## 注意事项

- 需要Node.js 18+
- 首次运行会自动下载浏览器
- 建议使用可视化模式观察执行过程
- 符号显示已优化为ASCII兼容格式
