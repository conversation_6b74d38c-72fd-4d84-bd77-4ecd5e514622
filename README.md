# Sentra Auto Browser

🤖 **智能浏览器自动化工具**

一个功能强大的 Node.js 浏览器自动化解决方案，集成了先进的 AI 能力，包括记忆系统、智能规划、自我反思和错误恢复等功能。

## ✨ 功能特性

### 核心能力
- 🎯 **智能自动化** - AI 驱动的浏览器控制，具备高级推理能力
- 🤖 **多模型支持** - 支持 OpenAI、Anthropic、Google Gemini 等主流 LLM
- 🌐 **全面操作** - 20+ 种操作类型，包括拖拽、文件上传、标签页管理等
- 📱 **视觉理解** - 基于截图的理解和可视化调试
- ⚡ **高性能** - 优化的执行引擎和性能监控

### 高级功能
- 🧠 **记忆系统** - 持久化记忆，支持语义搜索和模式识别
- 📋 **智能规划** - 多步骤规划，包含风险评估和备选方案
- 🤔 **自我反思** - 持续自我评估和策略调整
- 🔄 **错误恢复** - 自动错误处理，多种恢复策略
- 📊 **性能监控** - 实时指标监控和优化建议
- 🎛️ **高级配置** - 丰富的自定义配置选项

## 🚀 快速开始

### 安装

```bash
# 克隆仓库
git clone <repository-url>
cd sentra-auto-browser

# 安装依赖
npm install

# 构建项目
npm run build

# 设置环境变量
cp .env.example .env
# 编辑 .env 文件，添加你的 API 密钥
```

### 基本使用

```bash
# 运行简单任务
npx sentra-auto run "访问谷歌搜索 Node.js"

# 使用可视化浏览器
npx sentra-auto run "预订去东京的机票" --visible

# 指定特定的 LLM
npx sentra-auto run "查询巴黎的天气" --provider google --model gemini-1.5-pro
```

### Environment Setup

Create a `.env` file with your API keys:

```env
# Choose one LLM provider
OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here  
# GOOGLE_API_KEY=your_google_api_key_here

# Optional: Browser settings
BROWSER_HEADLESS=true
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720
```

## 📖 Usage Examples

### Command Line Interface

```bash
# Basic task execution
browser-use run "Go to amazon.com and find the price of iPhone 15"

# With custom settings
browser-use run "Search for restaurants near me" \
  --visible \
  --max-steps 20 \
  --provider anthropic \
  --model claude-3-5-sonnet-20241022

# Test your configuration
browser-use test

# View current configuration
browser-use config
```

### Programmatic Usage

```typescript
import { Agent, BrowserSession, LLMFactory, Config } from 'browser-use-nodejs';

async function main() {
  // Get configuration
  const llmConfig = Config.getLLMConfig();
  const browserProfile = Config.getBrowserProfile();
  const agentSettings = Config.getAgentSettings();

  // Create LLM instance
  const llm = LLMFactory.createLLM(llmConfig);

  // Create browser session
  const browserSession = new BrowserSession(browserProfile);
  await browserSession.start();

  // Create and run agent
  const agent = new Agent(
    "Go to github.com and find the most popular JavaScript repository",
    llm,
    browserSession,
    agentSettings
  );

  const history = await agent.run();
  
  console.log(`Task completed: ${history.success}`);
  console.log(`Steps taken: ${history.steps.length}`);
  
  await browserSession.close();
}

main().catch(console.error);
```

### Custom Browser Configuration

```typescript
import { BrowserSession } from 'browser-use-nodejs';

const browserSession = new BrowserSession({
  headless: false,
  viewport: { width: 1920, height: 1080 },
  userDataDir: './browser-profile',
  timeout: 60000,
});
```

### Custom LLM Configuration

```typescript
import { LLMFactory } from 'browser-use-nodejs';

const llm = LLMFactory.createLLM({
  provider: 'openai',
  model: 'gpt-4o',
  apiKey: 'your-api-key',
  temperature: 0.1,
});
```

## 🛠️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | - |
| `ANTHROPIC_API_KEY` | Anthropic API key | - |
| `GOOGLE_API_KEY` | Google API key | - |
| `BROWSER_HEADLESS` | Run browser in headless mode | `true` |
| `BROWSER_WIDTH` | Browser viewport width | `1280` |
| `BROWSER_HEIGHT` | Browser viewport height | `720` |
| `AGENT_MAX_STEPS` | Maximum steps per task | `50` |
| `AGENT_USE_VISION` | Enable screenshot analysis | `true` |
| `LOG_LEVEL` | Logging level (debug, info, warn, error) | `info` |

### CLI Options

```bash
browser-use run <task> [options]

Options:
  -h, --headless          Run browser in headless mode
  -v, --visible           Run browser in visible mode
  --no-vision            Disable vision/screenshot capabilities
  --max-steps <number>   Maximum number of steps (default: 50)
  --provider <provider>  LLM provider (openai, anthropic, google)
  --model <model>        LLM model name
  --temperature <number> LLM temperature (default: 0)
  --debug                Enable debug logging
```

## 🏗️ Architecture

```
src/
├── agent/          # Core agent logic
├── browser/        # Browser session management
├── controller/     # Action execution
├── dom/           # DOM analysis and interaction
├── llm/           # LLM integrations
├── utils/         # Utilities and helpers
├── config/        # Configuration management
└── cli/           # Command-line interface
```

## 🔧 Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Build the project
npm run build

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

## 📝 Supported Actions

The agent can perform these actions:

- **Click** - Click on elements
- **Type** - Enter text into input fields
- **Navigate** - Go to URLs
- **Scroll** - Scroll up/down on pages
- **Wait** - Wait for specified time
- **Done** - Mark task as complete

## 🤖 Supported LLM Providers

- **OpenAI** - GPT-4o, GPT-4o-mini, etc.
- **Anthropic** - Claude 3.5 Sonnet, Claude 3 Opus, etc.
- **Google** - Gemini 2.0 Flash, Gemini Pro, etc.

## 🆚 Differences from Python Version

This Node.js version is intentionally simplified:

**Removed:**
- Complex TUI interface
- Memory/embedding features
- Evaluation system
- Multiple browser support
- Telemetry and monitoring
- Complex configuration management

**Simplified:**
- Single browser engine (Chromium via Playwright)
- Basic CLI interface
- Essential actions only
- Streamlined LLM integration
- Minimal dependencies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if needed
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

Based on the original [browser-use](https://github.com/browser-use/browser-use) Python project.
