import { GoogleGenerativeAI } from '@google/generative-ai';
import { BaseLLM, LLMMessage, LLMResponse } from './base';
import { LLMConfig } from '../types';
import { logger } from '../utils/logger';

export class GoogleLLM extends BaseLLM {
  private client: GoogleGenerativeAI;

  constructor(config: LLMConfig) {
    super(config);
    this.client = new GoogleGenerativeAI(config.apiKey);
  }

  async generateResponse(messages: LLMMessage[]): Promise<LLMResponse> {
    try {
      logger.debug(`Sending request to Google with model: ${this.config.model}`, 'GoogleLLM');

      const model = this.client.getGenerativeModel({ 
        model: this.config.model,
        generationConfig: {
          temperature: this.config.temperature || 0,
          maxOutputTokens: 4000,
        }
      });

      // Convert messages to Google format
      const contents = this.convertMessagesToGoogleFormat(messages);

      const result = await model.generateContent({
        contents: contents
      });
      const response = result.response;
      const text = response.text();

      if (!text) {
        throw new Error('Invalid response from Google');
      }

      // Google doesn't provide detailed usage info in the same way
      const usage = {
        promptTokens: 0, // Not available
        completionTokens: 0, // Not available
        totalTokens: 0, // Not available
      };

      logger.debug(`Google response received`, 'GoogleLLM');

      return {
        content: text,
        usage,
      };
    } catch (error) {
      logger.error('Google API request failed', error as Error, 'GoogleLLM');
      throw error;
    }
  }

  private convertMessagesToGoogleFormat(messages: LLMMessage[]): any {
    const systemMessage = messages.find(msg => msg.role === 'system');
    const otherMessages = messages.filter(msg => msg.role !== 'system');

    // Combine system message with first user message if present
    let combinedContent = '';
    if (systemMessage && typeof systemMessage.content === 'string') {
      combinedContent = systemMessage.content + '\n\n';
    }

    const contents: any[] = [];

    for (let i = 0; i < otherMessages.length; i++) {
      const msg = otherMessages[i];
      const role = msg.role === 'assistant' ? 'model' : 'user';

      let textContent = '';
      let parts: any[] = [];

      if (typeof msg.content === 'string') {
        textContent = msg.content;
        // Add system message to first user message
        if (i === 0 && msg.role === 'user' && combinedContent) {
          textContent = combinedContent + textContent;
        }
        parts = [{ text: textContent }];
      } else {
        // Handle array content (text + images)
        for (const item of msg.content) {
          if (item.type === 'text') {
            let text = item.text || '';
            // Add system message to first user message
            if (i === 0 && msg.role === 'user' && combinedContent) {
              text = combinedContent + text;
              combinedContent = ''; // Only add once
            }
            parts.push({ text });
          } else if (item.type === 'image_url') {
            // Convert base64 image to Google format
            const base64Data = item.image_url!.url.replace('data:image/png;base64,', '');
            parts.push({
              inlineData: {
                mimeType: 'image/png',
                data: base64Data
              }
            });
          }
        }
      }

      contents.push({
        role,
        parts
      });
    }

    return contents;
  }
}
