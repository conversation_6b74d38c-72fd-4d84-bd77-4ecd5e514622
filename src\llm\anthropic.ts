import Anthropic from '@anthropic-ai/sdk';
import { BaseLLM, LLMMessage, LLMResponse } from './base';
import { LLMConfig } from '../types';
import { logger } from '../utils/logger';

export class AnthropicLLM extends BaseLLM {
  private client: Anthropic;

  constructor(config: LLMConfig) {
    super(config);
    this.client = new Anthropic({
      apiKey: config.apiKey,
    });
  }

  async generateResponse(messages: LLMMessage[]): Promise<LLMResponse> {
    try {
      logger.debug(`Sending request to Anthropic with model: ${this.config.model}`, 'AnthropicLLM');

      // Separate system message from other messages
      const systemMessage = messages.find(msg => msg.role === 'system');
      const otherMessages = messages.filter(msg => msg.role !== 'system');

      const response = await this.client.messages.create({
        model: this.config.model,
        max_tokens: 4000,
        temperature: this.config.temperature || 0,
        system: systemMessage?.content as string || '',
        messages: otherMessages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: this.formatContent(msg.content)
        })),
      });

      if (!response.content || response.content.length === 0) {
        throw new Error('Invalid response from Anthropic');
      }

      const textContent = response.content.find(block => block.type === 'text');
      if (!textContent || !('text' in textContent)) {
        throw new Error('No text content in Anthropic response');
      }

      const usage = response.usage ? {
        promptTokens: response.usage.input_tokens,
        completionTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens,
      } : undefined;

      logger.debug(`Anthropic response received, tokens: ${usage?.totalTokens || 'unknown'}`, 'AnthropicLLM');

      return {
        content: textContent.text,
        usage,
      };
    } catch (error) {
      logger.error('Anthropic API request failed', error as Error, 'AnthropicLLM');
      throw error;
    }
  }

  private formatContent(content: string | Array<any>): any {
    if (typeof content === 'string') {
      return content;
    }

    // Handle array content (text + images)
    return content.map(item => {
      if (item.type === 'text') {
        return {
          type: 'text',
          text: item.text
        };
      } else if (item.type === 'image_url') {
        // Convert OpenAI format to Anthropic format
        const base64Data = item.image_url.url.replace('data:image/png;base64,', '');
        return {
          type: 'image',
          source: {
            type: 'base64',
            media_type: 'image/png',
            data: base64Data
          }
        };
      }
      return item;
    });
  }
}
