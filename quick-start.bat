@echo off
echo ========================================
echo Sentra Auto Browser - Quick Start
echo ========================================

REM Check if project is built
if not exist dist\cli\index.js (
    echo Project not built yet. Running setup...
    call setup.bat
    if errorlevel 1 (
        echo Setup failed!
        pause
        exit /b 1
    )
)

echo.
echo Choose a quick start option:
echo 1. Visit Baidu homepage
echo 2. Search Node.js on Baidu
echo 3. Search JavaScript on Google
echo 4. Custom task
echo 5. Show configuration
echo 6. Test connection
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    echo Running: Visit Baidu homepage
    node dist/cli/index.js run "visit baidu homepage" --provider google --model gemini-1.5-flash --max-steps 10
) else if "%choice%"=="2" (
    echo Running: Search Node.js on Baidu
    node dist/cli/index.js run "visit baidu and search for Node.js" --provider google --model gemini-1.5-flash --max-steps 15
) else if "%choice%"=="3" (
    echo Running: Search JavaScript on Google
    node dist/cli/index.js run "visit google and search for JavaScript" --provider google --model gemini-1.5-flash --max-steps 15
) else if "%choice%"=="4" (
    set /p task="Enter your custom task: "
    echo Running custom task: !task!
    node dist/cli/index.js run "!task!" --provider google --model gemini-1.5-flash --max-steps 20
) else if "%choice%"=="5" (
    echo Showing configuration...
    node dist/cli/index.js config
) else if "%choice%"=="6" (
    echo Testing connection...
    node dist/cli/index.js test
) else (
    echo Invalid choice!
)

echo.
pause
