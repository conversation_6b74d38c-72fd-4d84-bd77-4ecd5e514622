# LLM 配置
# 根据你的偏好设置其中一个 API 密钥

# OpenAI (注释掉，不使用)
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_MODEL=gpt-4o

# Anthropic (注释掉，不使用)
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# Google Gemini (主要使用)
GOOGLE_API_KEY=AIzaSyC0JZIYhdJd16QRhU7KkS84MK9gGEmeHmo
GOOGLE_MODEL=gemini-2.5-flash

# LLM Settings
LLM_TEMPERATURE=0

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720
BROWSER_TIMEOUT=30000
# BROWSER_USER_DATA_DIR=/path/to/browser/profile
# BROWSER_EXECUTABLE_PATH=/path/to/browser/executable

# Agent Configuration
AGENT_MAX_STEPS=50
AGENT_MAX_ACTIONS_PER_STEP=3
AGENT_USE_VISION=true

# Logging
LOG_LEVEL=info
DEBUG=false
