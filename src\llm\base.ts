import { LLMConfig } from '../types';

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<{ type: 'text' | 'image_url'; text?: string; image_url?: { url: string } }>;
}

export interface LLMResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export abstract class BaseLLM {
  protected config: LLMConfig;

  constructor(config: LLMConfig) {
    this.config = config;
  }

  abstract generateResponse(messages: LLMMessage[]): Promise<LLMResponse>;

  protected formatSystemPrompt(): string {
    return `You are a browser automation agent. Your task is to help users automate web interactions.

You will be given:
1. A task description
2. The current state of the webpage (DOM elements and screenshot)
3. Previous actions taken

You should respond with the next action to take. Available actions are:

1. **click** - Click on an element
   Format: {"type": "click", "index": <element_index>, "xpath": "<optional_xpath>"}

2. **type** - Type text into an input field
   Format: {"type": "type", "index": <element_index>, "text": "<text_to_type>", "xpath": "<optional_xpath>"}

3. **navigate** - Navigate to a URL
   Format: {"type": "navigate", "url": "<url>"}

4. **scroll** - Scroll the page
   Format: {"type": "scroll", "direction": "up|down", "amount": <optional_pixels>}

5. **wait** - Wait for a specified time
   Format: {"type": "wait", "seconds": <number>}

6. **done** - Mark the task as complete
   Format: {"type": "done", "message": "<completion_message>", "success": true|false}

Rules:
- Always use the element index from the provided DOM elements list
- Be precise and only take one action at a time
- If you can't find the right element, try scrolling or navigating
- Use the screenshot to understand the visual context
- Respond with valid JSON only
- If the task is complete, use the "done" action

Current task: {task}`;
  }

  protected createUserMessage(task: string, domState: any, screenshot?: string): LLMMessage {
    const content: any[] = [
      {
        type: 'text',
        text: `Task: ${task}

Current page URL: ${domState.url}
Page title: ${domState.title}

Available elements:
${domState.elements.map((el: any, i: number) => 
  `${i}. ${el.tag} - "${el.text}" (${el.xpath})`
).join('\n')}

Please provide the next action as JSON.`
      }
    ];

    if (screenshot) {
      content.push({
        type: 'image_url',
        image_url: {
          url: `data:image/png;base64,${screenshot}`
        }
      });
    }

    return {
      role: 'user',
      content
    };
  }

  async generateAction(task: string, domState: any, screenshot?: string): Promise<any> {
    const messages: LLMMessage[] = [
      {
        role: 'system',
        content: this.formatSystemPrompt().replace('{task}', task)
      },
      this.createUserMessage(task, domState, screenshot)
    ];

    const response = await this.generateResponse(messages);
    
    try {
      return JSON.parse(response.content);
    } catch (error) {
      // If JSON parsing fails, try to extract JSON from the response
      const jsonMatch = response.content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      throw new Error(`Failed to parse LLM response as JSON: ${response.content}`);
    }
  }
}
