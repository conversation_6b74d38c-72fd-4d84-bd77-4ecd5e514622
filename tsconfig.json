{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022", "DOM", "DOM.Iterable"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "noImplicitAny": false, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}