{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "sourceMap": false, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "noImplicitAny": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}