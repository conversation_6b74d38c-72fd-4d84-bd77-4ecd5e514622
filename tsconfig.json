{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "sourceMap": false, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "noImplicitAny": false, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}