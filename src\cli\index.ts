#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { Agent } from '../agent/service';
import { BrowserSession } from '../browser/session';
import { LLMFactory } from '../llm/factory';
import Config from '../config';
import { logger } from '../utils/logger';

const program = new Command();

program
  .name('sentra-auto')
  .description('智能浏览器自动化工具 - 基于AI的网页操作助手')
  .version('1.0.0');

program
  .command('run')
  .description('运行浏览器自动化任务')
  .argument('<task>', '要执行的任务描述')
  .option('-h, --headless', '无头模式运行浏览器', false)
  .option('-v, --visible', '可视化模式运行浏览器', false)
  .option('--no-vision', '禁用视觉/截图功能')
  .option('--max-steps <number>', '最大执行步数', '50')
  .option('--provider <provider>', 'LLM 提供商 (openai, anthropic, google)')
  .option('--model <model>', 'LLM 模型名称')
  .option('--temperature <number>', 'LLM 温度参数', '0')
  .option('--debug', '启用调试日志')
  .action(async (task: string, options) => {
    try {
      // Set debug mode if requested
      if (options.debug) {
        process.env.LOG_LEVEL = 'debug';
      }

      console.log(chalk.blue.bold('\n🤖 Sentra Auto Browser - 智能浏览器自动化\n'));

      // Get LLM configuration
      let llmConfig: any;
      try {
        llmConfig = Config.getLLMConfig();

        // Override with CLI options if provided
        if (options.provider) {
          llmConfig.provider = options.provider as any;
          console.log(`🔧 Using CLI provider override: ${options.provider}`);
        }
        if (options.model) {
          llmConfig.model = options.model;
          console.log(`🔧 Using CLI model override: ${options.model}`);
        }
        if (options.temperature) {
          llmConfig.temperature = parseFloat(options.temperature);
        }
      } catch (error) {
        console.error(chalk.red('❌ LLM configuration error:'), error instanceof Error ? error.message : String(error));
        console.log(chalk.yellow('\n💡 请检查 .env 文件中的 API 密钥配置'));
        process.exit(1);
      }

      // Get browser profile
      const browserProfile = Config.getBrowserProfile();
      
      // Override headless setting
      if (options.visible) {
        browserProfile.headless = false;
      } else if (options.headless) {
        browserProfile.headless = true;
      }

      // Get agent settings
      const agentSettings = Config.getAgentSettings();
      
      // Override with CLI options
      if (options.maxSteps) agentSettings.maxSteps = parseInt(options.maxSteps);
      if (options.noVision) agentSettings.useVision = false;

      console.log(chalk.gray(`📋 任务: ${task}`));
      console.log(chalk.gray(`🤖 模型: ${llmConfig.provider} - ${llmConfig.model}`));
      console.log(chalk.gray(`🌐 浏览器: ${browserProfile.headless ? '无头模式' : '可视化模式'}`));
      console.log(chalk.gray(`👁️  视觉: ${agentSettings.useVision ? '已启用' : '已禁用'}`));
      console.log('');

      // 创建 LLM 实例
      const spinner = ora('正在初始化 AI 模型...').start();
      LLMFactory.validateConfig(llmConfig);
      const llm = LLMFactory.createLLM(llmConfig);
      spinner.succeed('AI 模型初始化完成');

      // 创建浏览器会话
      spinner.start('正在启动浏览器...');
      const browserSession = new BrowserSession(browserProfile);
      await browserSession.start();
      spinner.succeed('浏览器启动完成');

      // 创建并运行代理
      spinner.start('正在创建智能代理...');
      const agent = new Agent(task, llm, browserSession, agentSettings);
      spinner.succeed('智能代理创建完成');

      console.log(chalk.yellow('\n🚀 开始执行任务...\n'));

      // 运行代理
      const history = await agent.run();

      // 显示结果
      console.log(chalk.green.bold('\n✅ 任务执行完成！\n'));

      console.log(chalk.blue('📊 执行摘要:'));
      console.log(chalk.gray(`   执行步数: ${history.steps.length}`));
      console.log(chalk.gray(`   执行时长: ${history.totalDuration.toFixed(2)} 秒`));
      console.log(chalk.gray(`   执行结果: ${history.success ? '✅ 成功' : '❌ 失败'}`));
      
      if (history.steps.length > 0) {
        const lastStep = history.steps[history.steps.length - 1];
        if (lastStep.action.type === 'done') {
          console.log(chalk.gray(`   Final message: ${lastStep.action.message}`));
        }
      }

      // Close browser
      spinner.start('Closing browser...');
      await browserSession.close();
      spinner.succeed('Browser closed');

      console.log(chalk.green('\n🎉 All done!\n'));

      // Exit with appropriate code
      process.exit(history.success ? 0 : 1);

    } catch (error) {
      console.error(chalk.red('\n❌ Error:'), error instanceof Error ? error.message : String(error));
      
      if (options.debug && error instanceof Error) {
        console.error(chalk.red('\nStack trace:'));
        console.error(chalk.gray(error.stack));
      }
      
      process.exit(1);
    }
  });

program
  .command('config')
  .description('Show current configuration')
  .action(() => {
    try {
      console.log(chalk.blue.bold('\n🔧 Current Configuration\n'));

      const llmConfig = Config.getLLMConfig();
      const browserProfile = Config.getBrowserProfile();
      const agentSettings = Config.getAgentSettings();

      console.log(chalk.yellow('LLM Configuration:'));
      console.log(chalk.gray(`  Provider: ${llmConfig.provider}`));
      console.log(chalk.gray(`  Model: ${llmConfig.model}`));
      console.log(chalk.gray(`  Temperature: ${llmConfig.temperature}`));
      console.log(chalk.gray(`  API Key: ${llmConfig.apiKey ? '***' + llmConfig.apiKey.slice(-4) : 'Not set'}`));

      console.log(chalk.yellow('\nBrowser Configuration:'));
      console.log(chalk.gray(`  Headless: ${browserProfile.headless}`));
      console.log(chalk.gray(`  Viewport: ${browserProfile.viewport?.width}x${browserProfile.viewport?.height}`));
      console.log(chalk.gray(`  Timeout: ${browserProfile.timeout}ms`));

      console.log(chalk.yellow('\nAgent Configuration:'));
      console.log(chalk.gray(`  Max Steps: ${agentSettings.maxSteps}`));
      console.log(chalk.gray(`  Max Actions Per Step: ${agentSettings.maxActionsPerStep}`));
      console.log(chalk.gray(`  Use Vision: ${agentSettings.useVision}`));

      console.log(chalk.yellow('\nEnvironment Variables:'));
      console.log(chalk.gray(`  LOG_LEVEL: ${Config.getLogLevel()}`));
      console.log(chalk.gray(`  DEBUG: ${Config.isDebugMode()}`));

      console.log('');
    } catch (error) {
      console.error(chalk.red('Error reading configuration:'), error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

program
  .command('test')
  .description('Test browser and LLM connectivity')
  .action(async () => {
    try {
      console.log(chalk.blue.bold('\n🧪 Testing Configuration\n'));

      // Test LLM
      const spinner = ora('Testing LLM connection...').start();
      const llmConfig = Config.getLLMConfig();
      LLMFactory.validateConfig(llmConfig);
      const llm = LLMFactory.createLLM(llmConfig);
      
      // Simple test message
      const testResponse = await llm.generateResponse([
        { role: 'user', content: 'Hello, please respond with "OK" if you can understand this message.' }
      ]);
      
      if (testResponse.content.toLowerCase().includes('ok')) {
        spinner.succeed('LLM connection successful');
      } else {
        spinner.warn('LLM responded but may not be working correctly');
      }

      // Test browser
      spinner.start('Testing browser startup...');
      const browserProfile = Config.getBrowserProfile();
      const browserSession = new BrowserSession(browserProfile);
      await browserSession.start();
      
      await browserSession.navigate('https://example.com');
      const title = await browserSession.getCurrentTitle();
      
      await browserSession.close();
      
      if (title) {
        spinner.succeed('Browser connection successful');
      } else {
        spinner.warn('Browser started but may not be working correctly');
      }

      console.log(chalk.green('\n✅ All tests passed!\n'));

    } catch (error) {
      console.error(chalk.red('\n❌ Test failed:'), error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error(chalk.red('\n💥 Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red('\n💥 Unhandled Rejection:'), reason);
  process.exit(1);
});

// Parse command line arguments
program.parse();
