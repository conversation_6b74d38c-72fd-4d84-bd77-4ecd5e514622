# Sentra Auto Browser - 安装指南

## 问题解决方案

### 常见错误及解决方法

#### 1. "Cannot find module 'commander'" 错误
**原因：** 依赖未正确安装或版本冲突
**解决方法：**
```bash
# 方法1：使用clean-install.bat（推荐）
clean-install.bat

# 方法2：手动清理安装
rmdir /s /q node_modules
del package-lock.json
npm cache clean --force
npm install --legacy-peer-deps
npm run build
```

#### 2. "Cannot read properties of null" 错误
**原因：** npm版本冲突或依赖解析问题
**解决方法：**
```bash
# 更新npm到最新版本
npm install -g npm@latest

# 使用yarn替代npm
npm install -g yarn
yarn install
yarn build
```

#### 3. TypeScript编译错误
**原因：** TypeScript版本不兼容
**解决方法：**
```bash
# 全局安装TypeScript
npm install -g typescript@4.9.5

# 或使用npx
npx tsc --version
npx tsc
```

#### 4. 模块解析错误（如ansi-styles）
**原因：** 依赖版本不匹配
**解决方法：**
```bash
# 强制重新安装
npm install --force
# 或
npm install --legacy-peer-deps --no-audit
```

## 推荐安装步骤

### 方法1：自动安装（推荐）
```bash
# 运行自动安装脚本
clean-install.bat
```

### 方法2：手动安装
```bash
# 1. 清理环境
rmdir /s /q node_modules
del package-lock.json
npm cache clean --force

# 2. 安装依赖
npm install --legacy-peer-deps --no-audit --no-fund

# 3. 构建项目
npm run build

# 4. 安装浏览器
npx playwright install chromium

# 5. 测试安装
node dist/cli/index.js --help
```

### 方法3：使用yarn
```bash
# 1. 安装yarn
npm install -g yarn

# 2. 清理并安装
rmdir /s /q node_modules
del package-lock.json
yarn install

# 3. 构建
yarn build

# 4. 测试
node dist/cli/index.js --help
```

## 环境要求

- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **操作系统**: Windows 10/11, macOS, Linux

## 验证安装

运行以下命令验证安装是否成功：

```bash
# 检查版本
node --version
npm --version

# 检查项目结构
dir dist\cli\index.js

# 测试CLI
node dist/cli/index.js --help
node dist/cli/index.js config
```

## 快速开始

安装成功后，可以使用以下方式快速开始：

```bash
# 方式1：使用快速启动脚本
quick-start.bat

# 方式2：直接运行任务
run.bat "visit baidu homepage"

# 方式3：使用npm scripts
npm run baidu
npm run google
```

## 故障排除

如果仍然遇到问题，请按以下步骤排查：

1. **检查Node.js版本**
   ```bash
   node --version
   # 应该显示 v18.x.x 或更高
   ```

2. **检查npm版本**
   ```bash
   npm --version
   # 应该显示 8.x.x 或更高
   ```

3. **检查网络连接**
   ```bash
   npm config get registry
   # 如果在中国，可以设置淘宝镜像
   npm config set registry https://registry.npmmirror.com
   ```

4. **检查权限**
   - 确保以管理员身份运行命令提示符
   - 确保有写入当前目录的权限

5. **完全重置**
   ```bash
   # 删除整个项目文件夹，重新解压
   # 然后运行 clean-install.bat
   ```

## 联系支持

如果以上方法都无法解决问题，请提供以下信息：

1. 操作系统版本
2. Node.js版本 (`node --version`)
3. npm版本 (`npm --version`)
4. 完整的错误信息
5. 执行的命令

## 常用命令参考

```bash
# 安装相关
clean-install.bat          # 完全重新安装
npm install --force        # 强制安装
npm cache clean --force    # 清理缓存

# 构建相关
npm run clean              # 清理构建文件
npm run build              # 构建项目
npx tsc                    # 直接运行TypeScript编译

# 运行相关
quick-start.bat            # 交互式启动
run.bat "task"             # 运行特定任务
node dist/cli/index.js --help  # 查看帮助
```
