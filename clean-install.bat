@echo off
echo ========================================
echo Sentra Auto Browser - Clean Install
echo ========================================

REM Step 1: Clean everything
echo [1/6] Cleaning previous installation...
if exist node_modules (
    echo Removing node_modules...
    rmdir /s /q node_modules
)
if exist dist (
    echo Removing dist...
    rmdir /s /q dist
)
if exist package-lock.json (
    echo Removing package-lock.json...
    del package-lock.json
)

REM Step 2: Clear npm cache
echo [2/6] Clearing npm cache...
call npm cache clean --force

REM Step 3: Install dependencies with specific flags
echo [3/6] Installing dependencies...
call npm install --no-package-lock --legacy-peer-deps --no-audit --no-fund
if errorlevel 1 (
    echo ERROR: npm install failed!
    echo Trying alternative method...
    call npm install --force
    if errorlevel 1 (
        echo ERROR: All installation methods failed!
        echo Please check:
        echo 1. Your internet connection
        echo 2. Node.js version (should be 18+)
        echo 3. npm version
        pause
        exit /b 1
    )
)

REM Step 4: Build project
echo [4/6] Building project...
call npm run build
if errorlevel 1 (
    echo ERROR: Build failed!
    echo Checking TypeScript installation...
    call npx tsc --version
    if errorlevel 1 (
        echo Installing TypeScript globally...
        call npm install -g typescript
        call npm run build
        if errorlevel 1 (
            echo ERROR: Build still failed!
            pause
            exit /b 1
        )
    )
)

REM Step 5: Install browser
echo [5/6] Installing browser...
call npx playwright install chromium
if errorlevel 1 (
    echo WARNING: Browser installation failed, but continuing...
)

REM Step 6: Test installation
echo [6/6] Testing installation...
node dist/cli/index.js --help
if errorlevel 1 (
    echo ERROR: Installation test failed!
    pause
    exit /b 1
)

echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Copy .env.example to .env and add your API keys
echo 2. Run: quick-start.bat for interactive usage
echo 3. Or use: run.bat "your task description"
echo.
pause
