// import type { Page } from 'playwright';
import { DOMState, DOMElement } from '../types';
import { logger } from '../utils/logger';

export class DOMService {
  private page: any;

  constructor(page: any) {
    this.page = page;
  }

  async getDOMState(): Promise<DOMState> {
    try {
      // Get basic page info
      const url = this.page.url();
      const title = await this.page.title();

      // Get clickable elements
      const elements = await this.getClickableElements();

      // Take screenshot
      const screenshot = await this.page.screenshot({ 
        type: 'png',
        fullPage: false 
      });

      return {
        elements,
        url,
        title,
        screenshot: screenshot.toString('base64'),
      };
    } catch (error) {
      logger.error('Failed to get DOM state', error as Error, 'DOMService');
      throw error;
    }
  }

  private async getClickableElements(): Promise<DOMElement[]> {
    try {
      // Inject our DOM analysis script
      const elements = await this.page.evaluate(() => {
        const results: any[] = [];
        let index = 0;

        // Function to check if element is visible
        function isElementVisible(element: Element): boolean {
          const rect = element.getBoundingClientRect();
          const style = window.getComputedStyle(element);
          
          return (
            rect.width > 0 &&
            rect.height > 0 &&
            style.visibility !== 'hidden' &&
            style.display !== 'none' &&
            style.opacity !== '0' &&
            rect.top < window.innerHeight &&
            rect.bottom > 0 &&
            rect.left < window.innerWidth &&
            rect.right > 0
          );
        }

        // Function to check if element is clickable
        function isElementClickable(element: Element): boolean {
          const tagName = element.tagName.toLowerCase();
          const role = element.getAttribute('role');
          const type = element.getAttribute('type');

          // Standard clickable elements
          if (['a', 'button', 'input', 'select', 'textarea'].includes(tagName)) {
            return true;
          }

          // Elements with click handlers or roles
          if (role && ['button', 'link', 'menuitem', 'tab'].includes(role)) {
            return true;
          }

          // Input elements
          if (tagName === 'input' && type && ['button', 'submit', 'reset', 'checkbox', 'radio'].includes(type)) {
            return true;
          }

          // Elements with onclick or cursor pointer
          const style = window.getComputedStyle(element);
          if (style.cursor === 'pointer') {
            return true;
          }

          // Check for event listeners (simplified)
          if (element.hasAttribute('onclick') || element.hasAttribute('href')) {
            return true;
          }

          return false;
        }

        // Function to get XPath
        function getXPath(element: Element): string {
          if (element.id) {
            return `//*[@id="${element.id}"]`;
          }
          
          const parts: string[] = [];
          let current: Element | null = element;
          
          while (current && current.nodeType === Node.ELEMENT_NODE) {
            let tagName = current.tagName.toLowerCase();
            let index = 1;
            
            // Count siblings with same tag name
            let sibling = current.previousElementSibling;
            while (sibling) {
              if (sibling.tagName.toLowerCase() === tagName) {
                index++;
              }
              sibling = sibling.previousElementSibling;
            }
            
            parts.unshift(`${tagName}[${index}]`);
            current = current.parentElement;
          }
          
          return '/' + parts.join('/');
        }

        // Function to get element text
        function getElementText(element: Element): string {
          // Try different text sources
          const textContent = element.textContent?.trim() || '';
          const value = (element as any).value || '';
          const placeholder = element.getAttribute('placeholder') || '';
          const alt = element.getAttribute('alt') || '';
          const title = element.getAttribute('title') || '';
          const ariaLabel = element.getAttribute('aria-label') || '';

          return textContent || value || placeholder || alt || title || ariaLabel || '';
        }

        // Find all potentially interactive elements
        const allElements = document.querySelectorAll('*');
        
        for (const element of allElements) {
          if (isElementVisible(element) && isElementClickable(element)) {
            // Add data attribute for indexing
            element.setAttribute('data-browser-use-index', index.toString());

            const domElement: any = {
              index,
              tag: element.tagName.toLowerCase(),
              text: getElementText(element),
              attributes: {} as Record<string, string>,
              xpath: getXPath(element),
              isClickable: true,
              isVisible: true,
            };

            // Get important attributes
            const importantAttrs = ['id', 'class', 'type', 'name', 'href', 'role', 'aria-label', 'placeholder'];
            for (const attr of importantAttrs) {
              const value = element.getAttribute(attr);
              if (value) {
                (domElement.attributes as Record<string, string>)[attr] = value;
              }
            }

            results.push(domElement);
            index++;
          }
        }

        return results;
      });

      logger.debug(`Found ${elements.length} clickable elements`, 'DOMService');
      return elements;
    } catch (error) {
      logger.error('Failed to get clickable elements', error as Error, 'DOMService');
      throw error;
    }
  }

  async highlightElement(index: number): Promise<void> {
    try {
      await this.page.evaluate((elementIndex) => {
        const element = document.querySelector(`[data-browser-use-index="${elementIndex}"]`) as HTMLElement;
        if (element) {
          element.style.outline = '3px solid red';
          element.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';

          // Remove highlight after 2 seconds
          setTimeout(() => {
            element.style.outline = '';
            element.style.backgroundColor = '';
          }, 2000);
        }
      }, index);
    } catch (error) {
      logger.error(`Failed to highlight element at index ${index}`, error as Error, 'DOMService');
    }
  }

  async scrollToElement(index: number): Promise<void> {
    try {
      await this.page.evaluate((elementIndex) => {
        const element = document.querySelector(`[data-browser-use-index="${elementIndex}"]`) as HTMLElement;
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, index);
      
      await this.page.waitForTimeout(500); // Wait for scroll to complete
    } catch (error) {
      logger.error(`Failed to scroll to element at index ${index}`, error as Error, 'DOMService');
    }
  }
}
