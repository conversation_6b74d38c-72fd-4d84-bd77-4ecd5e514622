# Sentra Auto Browser 使用指南

## 快速开始

### 1. 安装依赖
```bash
# 使用 cnpm 安装依赖（推荐）
cnpm install

# 或使用 npm
npm install
```

### 2. 构建项目
```bash
npm run build
```

### 3. 运行任务

#### 方式一：使用 npm scripts（推荐）
```bash
# 快速测试百度搜索
npm run baidu

# 快速测试Google搜索
npm run google

# 自定义任务
npm run quick "你的任务描述" -- --provider google --model gemini-1.5-flash
```

#### 方式二：使用批处理文件（Windows）
```bash
# 运行任务
run.bat "访问百度搜索Node.js" --provider google --model gemini-2.5-flash

# 查看帮助
run.bat --help
```

#### 方式三：使用shell脚本（Linux/Mac）
```bash
# 给脚本执行权限
chmod +x run.sh

# 运行任务
./run.sh "访问百度搜索Node.js" --provider google --model gemini-2.5-flash
```

#### 方式四：直接使用CLI
```bash
# 构建后直接运行
node dist/cli/index.js run "你的任务描述" --provider google --model gemini-1.5-flash
```

## 配置说明

### 环境变量配置
编辑 `.env` 文件：

```env
# Google AI (推荐)
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_MODEL=gemini-1.5-flash

# OpenAI (可选)
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_MODEL=gpt-4o

# Anthropic (可选)
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# 浏览器配置
BROWSER_HEADLESS=false  # 设置为true使用无头模式
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720

# 代理配置
AGENT_MAX_STEPS=50
AGENT_USE_VISION=true
```

### CLI 参数说明

```bash
node dist/cli/index.js run <任务描述> [选项]

选项：
  -h, --headless          无头模式运行浏览器
  -v, --visible           可视化模式运行浏览器
  --no-vision             禁用视觉/截图功能
  --max-steps <number>    最大执行步数 (默认: 50)
  --provider <provider>   LLM 提供商 (openai, anthropic, google)
  --model <model>         LLM 模型名称
  --temperature <number>  LLM 温度参数 (默认: 0)
  --debug                 启用调试日志
```

## 常用命令

### 查看配置
```bash
node dist/cli/index.js config
```

### 测试连接
```bash
node dist/cli/index.js test
```

### 查看帮助
```bash
node dist/cli/index.js --help
node dist/cli/index.js run --help
```

## 任务示例

### 搜索任务
```bash
# 百度搜索
npm run quick "访问百度搜索Python教程"

# Google搜索
npm run quick "访问Google搜索机器学习"

# 搜索并点击结果
npm run quick "访问百度搜索Node.js，点击第一个搜索结果"
```

### 网站操作
```bash
# 登录网站
npm run quick "访问GitHub登录页面，输入用户名和密码"

# 填写表单
npm run quick "访问联系我们页面，填写姓名、邮箱和留言"

# 购物操作
npm run quick "访问电商网站，搜索手机，查看第一个商品详情"
```

### 数据提取
```bash
# 提取新闻标题
npm run quick "访问新闻网站，提取今日头条新闻标题"

# 提取价格信息
npm run quick "访问购物网站，搜索笔记本电脑，提取前5个商品的价格"
```

## 故障排除

### 常见问题

1. **模块找不到错误**
   ```bash
   # 重新安装依赖
   rm -rf node_modules
   cnpm install
   npm run build
   ```

2. **浏览器启动失败**
   ```bash
   # 安装浏览器
   npx playwright install chromium
   ```

3. **API密钥错误**
   - 检查 `.env` 文件中的API密钥是否正确
   - 确保API密钥有足够的配额

4. **任务执行超时**
   - 增加 `--max-steps` 参数
   - 检查网络连接
   - 使用 `--debug` 查看详细日志

### 调试模式
```bash
# 启用调试日志
npm run quick "你的任务" -- --debug

# 查看详细错误信息
node dist/cli/index.js run "你的任务" --debug --visible
```

## 性能优化

### 提高执行速度
- 使用无头模式：`--headless`
- 禁用视觉功能：`--no-vision`
- 减少最大步数：`--max-steps 20`

### 提高成功率
- 使用可视化模式：`--visible`
- 启用视觉功能（默认开启）
- 增加最大步数：`--max-steps 100`
- 使用更强大的模型：`--model gemini-2.5-flash`
