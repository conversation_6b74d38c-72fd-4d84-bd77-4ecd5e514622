import { config } from 'dotenv';
import { LLMConfig, BrowserProfile, AgentSettings } from '../types';

// Load environment variables
config();

export class Config {
  // LLM Configuration
  static getLLMConfig(): LLMConfig {
    const openaiKey = process.env.OPENAI_API_KEY;
    const anthropicKey = process.env.ANTHROPIC_API_KEY;
    const googleKey = process.env.GOOGLE_API_KEY;

    // Auto-detect provider based on available API keys
    if (openaiKey) {
      return {
        provider: 'openai',
        model: process.env.OPENAI_MODEL || 'gpt-4o',
        apiKey: openaiKey,
        temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
      };
    } else if (anthropicKey) {
      return {
        provider: 'anthropic',
        model: process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022',
        apiKey: anthropicKey,
        temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
      };
    } else if (googleKey) {
      return {
        provider: 'google',
        model: process.env.GOOGLE_MODEL || 'gemini-2.0-flash-exp',
        apiKey: googleKey,
        temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
      };
    }

    throw new Error(
      'No LLM API key found. Please set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GOOGLE_API_KEY'
    );
  }

  // Browser Configuration
  static getBrowserProfile(): BrowserProfile {
    return {
      headless: process.env.BROWSER_HEADLESS === 'true',
      viewport: {
        width: parseInt(process.env.BROWSER_WIDTH || '1280'),
        height: parseInt(process.env.BROWSER_HEIGHT || '720'),
      },
      userDataDir: process.env.BROWSER_USER_DATA_DIR,
      executablePath: process.env.BROWSER_EXECUTABLE_PATH,
      timeout: parseInt(process.env.BROWSER_TIMEOUT || '30000'),
    };
  }

  // Agent Configuration
  static getAgentSettings(): AgentSettings {
    return {
      maxSteps: parseInt(process.env.AGENT_MAX_STEPS || '50'),
      maxActionsPerStep: parseInt(process.env.AGENT_MAX_ACTIONS_PER_STEP || '3'),
      useVision: process.env.AGENT_USE_VISION !== 'false',
      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0'),
    };
  }

  // Logging Configuration
  static getLogLevel(): string {
    return process.env.LOG_LEVEL || 'info';
  }

  // Debug mode
  static isDebugMode(): boolean {
    return process.env.DEBUG === 'true';
  }
}

export default Config;
