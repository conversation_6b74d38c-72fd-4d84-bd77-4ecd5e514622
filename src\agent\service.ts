import { BrowserSession } from '../browser/session';
import { Controller } from '../controller/service';
import { BaseLLM } from '../llm/base';
import { AgentSettings, AgentStep, AgentHistory, Action, ActionResult, DOMState, PlanningResult, ReflectionResult, PerformanceMetrics } from '../types';
import { logger } from '../utils/logger';
import { MemoryService } from '../memory/service';
import { PlanningService } from '../planning/service';
import { ReflectionService } from '../reflection/service';
import { ErrorRecoveryService } from '../recovery/service';
import { PerformanceMonitoringService } from '../monitoring/service';
import { Helpers } from '../utils/helpers';

// 智能代理类 - 核心自动化执行引擎
export class Agent {
  private task: string;                    // 任务描述
  private llm: BaseLLM;                   // 语言模型实例
  private browserSession: BrowserSession; // 浏览器会话
  private controller: Controller;          // 控制器
  private settings: AgentSettings;         // 代理设置
  private history: AgentStep[] = [];      // 执行历史
  private startTime: Date = new Date();   // 开始时间
  private completed: boolean = false;      // 是否完成
  private success: boolean = false;        // 是否成功

  // 高级服务模块
  private memoryService?: MemoryService;                    // 记忆服务
  private planningService?: PlanningService;                // 规划服务
  private reflectionService?: ReflectionService;            // 反思服务
  private errorRecoveryService?: ErrorRecoveryService;      // 错误恢复服务
  private performanceMonitoringService?: PerformanceMonitoringService; // 性能监控服务

  // 代理状态
  private agentId: string;              // 代理ID
  private sessionId: string;            // 会话ID
  private currentPlan?: PlanningResult; // 当前计划
  private lastReflection?: ReflectionResult; // 最后反思结果

  constructor(
    task: string,
    llm: BaseLLM,
    browserSession: BrowserSession,
    settings: AgentSettings = {}
  ) {
    this.task = task;
    this.llm = llm;
    this.browserSession = browserSession;
    this.controller = new Controller(browserSession);
    this.settings = {
      maxSteps: 50,
      maxActionsPerStep: 3,
      useVision: true,
      retryFailedActions: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableMemory: true,
      memorySize: 1000,
      enablePlanning: true,
      planningSteps: 10,
      enableReflection: true,
      reflectionInterval: 5,
      enableErrorRecovery: true,
      enablePerformanceMonitoring: true,
      enableScreenshotOnError: true,
      enableActionValidation: true,
      ...settings,
    };

    // Generate unique IDs
    this.agentId = this.generateId('agent');
    this.sessionId = this.generateId('session');

    // Initialize advanced services if enabled
    this.initializeServices();
  }

  async run(): Promise<AgentHistory> {
    try {
      logger.info(`开始执行代理任务: ${this.task}`, 'Agent');
      this.startTime = new Date();

      // 如果浏览器会话未启动，则启动它
      if (!this.browserSession.isStarted()) {
        await this.browserSession.start();
      }

      // 执行步骤直到完成或达到最大步数
      for (let stepNumber = 1; stepNumber <= this.settings.maxSteps!; stepNumber++) {
        logger.step(stepNumber, '分析当前状态并规划下一步操作');

        try {
          // 获取当前 DOM 状态
          const domState = await this.controller.getCurrentState();

          // 如果启用了视觉功能，则截图
          let screenshot: string | undefined;
          if (this.settings.useVision) {
            screenshot = await this.controller.takeScreenshot();
          }

          // 从 LLM 获取下一步操作
          const action = await this.llm.generateAction(this.task, domState, screenshot);

          // 验证操作
          this.validateAction(action);

          // 执行操作
          const result = await this.controller.executeAction(action);

          // Create step record
          const step: AgentStep = {
            stepNumber,
            action,
            result,
            domState,
            timestamp: new Date(),
          };

          this.history.push(step);

          // Log result
          logger.result(result.message || 'Action completed', result.success);

          // Check if task is complete
          if (action.type === 'done') {
            this.completed = true;
            this.success = action.success;
            logger.success(`Task completed: ${action.message}`, 'Agent');
            break;
          }

          // Check if action failed
          if (!result.success) {
            logger.warn(`Action failed, continuing with next step`, 'Agent');
          }

          // Small delay between actions
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          logger.error(`Step ${stepNumber} failed`, error as Error, 'Agent');
          
          // Create error step
          const errorStep: AgentStep = {
            stepNumber,
            action: { type: 'done', message: 'Error occurred', success: false } as Action,
            result: {
              success: false,
              error: error instanceof Error ? error.message : String(error),
              message: `Step failed: ${error instanceof Error ? error.message : String(error)}`,
            },
            timestamp: new Date(),
          };

          this.history.push(errorStep);
          
          // Continue to next step unless it's a critical error
          if (this.isCriticalError(error)) {
            break;
          }
        }
      }

      // If we reached max steps without completion
      if (!this.completed) {
        logger.warn(`Reached maximum steps (${this.settings.maxSteps}) without completion`, 'Agent');
        this.completed = true;
        this.success = false;
      }

      const totalDuration = (new Date().getTime() - this.startTime.getTime()) / 1000;
      logger.info(`Agent finished. Steps: ${this.history.length}, Duration: ${totalDuration.toFixed(2)}s`, 'Agent');

      return this.getHistory();

    } catch (error) {
      logger.error('Agent execution failed', error as Error, 'Agent');
      this.completed = true;
      this.success = false;
      throw error;
    }
  }



  isCompleted(): boolean {
    return this.completed;
  }

  isSuccessful(): boolean {
    return this.success;
  }

  getStepCount(): number {
    return this.history.length;
  }

  async stop(): Promise<void> {
    logger.info('Stopping agent execution', 'Agent');
    this.completed = true;
    this.success = false;
  }

  // Advanced agent methods
  private initializeServices(): void {
    if (this.settings.enableMemory) {
      this.memoryService = new MemoryService(this.settings.memorySize);
    }

    if (this.settings.enablePlanning) {
      this.planningService = new PlanningService(this.llm, this.memoryService);
    }

    if (this.settings.enableReflection) {
      this.reflectionService = new ReflectionService(this.llm, this.memoryService);
    }

    if (this.settings.enableErrorRecovery) {
      this.errorRecoveryService = new ErrorRecoveryService(this.browserSession, this.controller);
    }

    if (this.settings.enablePerformanceMonitoring) {
      this.performanceMonitoringService = new PerformanceMonitoringService();
      this.performanceMonitoringService.startMonitoring();
    }
  }

  private async createInitialPlan(domState: DOMState): Promise<void> {
    if (!this.planningService || !this.settings.enablePlanning) return;

    try {
      this.currentPlan = await this.planningService.createPlan(
        this.task,
        domState,
        this.settings.planningSteps
      );

      logger.info(`Created plan with ${this.currentPlan.plan.length} steps, confidence: ${this.currentPlan.confidence}`, 'Agent');
    } catch (error) {
      logger.warn('Failed to create initial plan, continuing without planning', 'Agent');
    }
  }

  private async updatePlan(stepNumber: number, domState: DOMState, lastResult?: ActionResult): Promise<void> {
    if (!this.planningService || !this.currentPlan) return;

    try {
      this.currentPlan = await this.planningService.updatePlan(
        this.currentPlan,
        stepNumber,
        domState,
        lastResult
      );
    } catch (error) {
      logger.warn('Failed to update plan', 'Agent');
    }
  }

  private async performReflection(stepNumber: number, domState: DOMState): Promise<void> {
    if (!this.reflectionService || !this.settings.enableReflection) return;

    if (stepNumber % this.settings.reflectionInterval! !== 0) return;

    try {
      const recentSteps = this.history.slice(-this.settings.reflectionInterval!);
      this.lastReflection = await this.reflectionService.reflect(
        this.task,
        recentSteps,
        domState,
        stepNumber
      );

      if (!this.lastReflection.shouldContinue) {
        logger.warn('Reflection suggests stopping execution', 'Agent');
        this.completed = true;
        this.success = false;
      }
    } catch (error) {
      logger.warn('Failed to perform reflection', 'Agent');
    }
  }

  private async handleActionError(
    error: Error,
    action: Action,
    stepNumber: number,
    domState: DOMState,
    retryCount: number
  ): Promise<ActionResult | null> {
    if (!this.errorRecoveryService || !this.settings.enableErrorRecovery) {
      return null;
    }

    try {
      const context = {
        stepNumber,
        previousActions: this.history.map(h => h.action),
        currentDOMState: domState,
        agentHistory: this.history,
        retryCount,
      };

      return await this.errorRecoveryService.handleError(error, context, action);
    } catch (recoveryError) {
      logger.error('Error recovery failed', recoveryError as Error, 'Agent');
      return null;
    }
  }

  private async recordPerformanceMetrics(action: Action, result: ActionResult, duration: number): Promise<void> {
    if (!this.performanceMonitoringService) return;

    this.performanceMonitoringService.recordActionEnd('', action, result.success, duration);

    if (!result.success) {
      this.performanceMonitoringService.recordError(new Error(result.error || 'Unknown error'), action);
    }
  }

  private async addToMemory(step: AgentStep): Promise<void> {
    if (!this.memoryService) return;

    try {
      await this.memoryService.addActionMemory(step);

      if (step.domState) {
        await this.memoryService.addObservationMemory(step.domState, step.stepNumber);
      }
    } catch (error) {
      logger.warn('Failed to add step to memory', 'Agent');
    }
  }

  private validateAction(action: any): void {
    if (!this.settings.enableActionValidation) return;

    if (!action || typeof action !== 'object') {
      throw new Error('Invalid action: must be an object');
    }

    if (!action.type) {
      throw new Error('Invalid action: missing type');
    }

    const validTypes = [
      'click', 'type', 'navigate', 'scroll', 'wait', 'done',
      'hover', 'drag_drop', 'key_press', 'select', 'upload_file',
      'take_screenshot', 'extract_data', 'execute_script',
      'switch_tab', 'new_tab', 'close_tab', 'go_back', 'go_forward',
      'refresh', 'set_cookie', 'wait_for_element', 'wait_for_navigation'
    ];

    if (!validTypes.includes(action.type)) {
      throw new Error(`Invalid action type: ${action.type}`);
    }

    // Type-specific validation
    switch (action.type) {
      case 'click':
      case 'hover':
        if (typeof action.index !== 'number') {
          throw new Error(`${action.type} action requires numeric index`);
        }
        break;
      case 'type':
        if (typeof action.index !== 'number' || typeof action.text !== 'string') {
          throw new Error('Type action requires numeric index and text string');
        }
        break;
      case 'navigate':
        if (typeof action.url !== 'string') {
          throw new Error('Navigate action requires url string');
        }
        break;
      case 'scroll':
        if (!['up', 'down'].includes(action.direction)) {
          throw new Error('Scroll action requires direction "up" or "down"');
        }
        break;
      case 'wait':
        if (typeof action.seconds !== 'number' || action.seconds <= 0) {
          throw new Error('Wait action requires positive number of seconds');
        }
        break;
      case 'done':
        if (typeof action.message !== 'string' || typeof action.success !== 'boolean') {
          throw new Error('Done action requires message string and success boolean');
        }
        break;
    }
  }

  private isCriticalError(error: any): boolean {
    const errorMessage = error instanceof Error ? error.message : String(error);

    const criticalErrors = [
      'Browser session not started',
      'Browser crashed',
      'Network error',
      'Authentication failed',
    ];

    return criticalErrors.some(criticalError =>
      errorMessage.toLowerCase().includes(criticalError.toLowerCase())
    );
  }

  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Enhanced getHistory method
  getHistory(): AgentHistory {
    const totalDuration = (new Date().getTime() - this.startTime.getTime()) / 1000;
    const successfulSteps = this.history.filter(s => s.result.success);
    const failedSteps = this.history.filter(s => !s.result.success);

    return {
      task: this.task,
      steps: this.history,
      completed: this.completed,
      success: this.success,
      totalDuration,
      startTime: this.startTime,
      endTime: this.completed ? new Date() : undefined,
      metadata: {
        agentId: this.agentId,
        sessionId: this.sessionId,
        llmProvider: 'unknown', // Would need to be passed from config
        llmModel: 'unknown',
        browserProfile: {},
        agentSettings: this.settings,
        totalTokensUsed: 0, // Would need to track from LLM responses
        totalCost: 0,
        averageStepDuration: this.history.length > 0
          ? totalDuration / this.history.length
          : 0,
        successRate: this.history.length > 0
          ? successfulSteps.length / this.history.length
          : 0,
        errorCount: failedSteps.length,
        retryCount: 0, // Would need to track retries
        screenshotCount: this.history.filter(s => s.result.screenshot).length,
        finalUrl: this.browserSession.getCurrentUrl(),
        finalTitle: undefined, // Would need async call
      },
      planning: this.currentPlan,
      reflection: this.lastReflection ? [this.lastReflection] : [],
      performance: this.performanceMonitoringService?.getMetrics(this.history),
    };
  }

  // Additional utility methods
  async getMemoryStats(): Promise<any> {
    return this.memoryService?.getMemoryStats() || null;
  }

  async searchMemory(query: string): Promise<any> {
    if (!this.memoryService) return [];
    return this.memoryService.searchMemories({ query, limit: 10 });
  }

  getPerformanceReport(): string {
    if (!this.performanceMonitoringService) return 'Performance monitoring not enabled';
    return this.performanceMonitoringService.generateReport(this.history);
  }

  getCurrentPlan(): PlanningResult | undefined {
    return this.currentPlan;
  }

  getLastReflection(): ReflectionResult | undefined {
    return this.lastReflection;
  }
}
