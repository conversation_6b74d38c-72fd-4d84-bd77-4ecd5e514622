{"name": "sentra-auto-browser", "version": "1.0.0", "description": "智能浏览器自动化工具 - 基于AI的网页操作助手", "main": "dist/index.js", "bin": {"sentra-auto": "dist/cli/index.js"}, "scripts": {"build": "tsc", "start": "node dist/cli/index.js", "clean": "node -e \"const fs=require('fs');if(fs.existsSync('dist'))fs.rmSync('dist',{recursive:true,force:true})\"", "prebuild": "npm run clean"}, "dependencies": {"@anthropic-ai/sdk": "0.54.0", "@google/generative-ai": "0.21.0", "chalk": "4.1.2", "commander": "9.5.0", "dotenv": "16.4.7", "fs-extra": "11.2.0", "lodash": "4.17.21", "openai": "4.81.0", "ora": "5.4.1", "playwright": "1.52.0", "uuid": "9.0.1"}, "devDependencies": {"@types/node": "18.19.0", "typescript": "4.9.5", "@types/uuid": "9.0.8", "@types/lodash": "4.17.13", "@types/fs-extra": "11.0.4"}, "engines": {"node": ">=18.0.0"}}