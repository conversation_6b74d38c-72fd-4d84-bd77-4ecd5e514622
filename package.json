{"name": "sentra-auto-browser", "version": "1.0.0", "description": "智能浏览器自动化工具 - 基于AI的网页操作助手", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"sentra-auto": "dist/cli/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/cli/index.ts", "start": "node dist/cli/index.js", "install-browser": "npx playwright install chromium", "clean": "node -e \"const fs=require('fs');const path=require('path');function rmdir(dir){if(fs.existsSync(dir)){fs.rmSync(dir,{recursive:true,force:true})}}rmdir('dist')\"", "prebuild": "npm run clean", "run:task": "npm run build && node dist/cli/index.js run", "quick": "npm run build && node dist/cli/index.js run", "baidu": "npm run build && node dist/cli/index.js run \"访问百度搜索Node.js\" --provider google --model gemini-1.5-flash", "google": "npm run build && node dist/cli/index.js run \"访问Google搜索JavaScript\" --provider google --model gemini-1.5-flash"}, "keywords": ["浏览器自动化", "智能代理", "AI助手", "网页操作", "playwright", "llm", "automation", "browser", "ai"], "author": "Sentra Auto Browser Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "0.54.0", "@google/generative-ai": "0.21.0", "chalk": "4.1.2", "commander": "9.5.0", "dotenv": "16.4.7", "fs-extra": "11.2.0", "lodash": "4.17.21", "openai": "4.81.0", "ora": "5.4.1", "playwright": "1.52.0", "uuid": "9.0.1"}, "devDependencies": {"@types/node": "18.19.0", "typescript": "4.9.5", "ts-node": "10.9.2", "@types/uuid": "9.0.8", "@types/lodash": "4.17.13", "@types/fs-extra": "11.0.4"}, "engines": {"node": ">=18.0.0"}}