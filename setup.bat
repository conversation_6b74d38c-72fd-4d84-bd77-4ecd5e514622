@echo off
echo ========================================
echo Sentra Auto Browser Setup Script
echo ========================================

REM Clean previous installation
echo [1/5] Cleaning previous installation...
if exist node_modules (
    rmdir /s /q node_modules
)
if exist dist (
    rmdir /s /q dist
)
if exist package-lock.json (
    del package-lock.json
)

REM Install dependencies using npm (more compatible than cnpm)
echo [2/5] Installing dependencies...
call npm install --legacy-peer-deps
if errorlevel 1 (
    echo ERROR: Failed to install dependencies!
    echo Trying with yarn...
    call yarn install
    if errorlevel 1 (
        echo ERROR: Both npm and yarn failed. Please check your network connection.
        pause
        exit /b 1
    )
)

REM Build project
echo [3/5] Building project...
call npm run build
if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

REM Install browser
echo [4/5] Installing browser...
call npx playwright install chromium
if errorlevel 1 (
    echo WARNING: Browser installation failed, but continuing...
)

REM Test configuration
echo [5/5] Testing configuration...
node dist/cli/index.js config
if errorlevel 1 (
    echo ERROR: Configuration test failed!
    pause
    exit /b 1
)

echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Usage examples:
echo   run.bat "visit baidu homepage"
echo   run.bat "search for Node.js on Google" --provider google --model gemini-1.5-flash
echo.
pause
