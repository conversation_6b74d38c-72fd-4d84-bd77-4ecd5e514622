import OpenAI from 'openai';
import { BaseLLM, LLMMessage, LLMResponse } from './base';
import { LLMConfig } from '../types';
import { logger } from '../utils/logger';

export class OpenAILLM extends BaseLLM {
  private client: OpenAI;

  constructor(config: LLMConfig) {
    super(config);
    this.client = new OpenAI({
      apiKey: config.apiKey,
    });
  }

  async generateResponse(messages: LLMMessage[]): Promise<LLMResponse> {
    try {
      logger.debug(`Sending request to OpenAI with model: ${this.config.model}`, 'OpenAILLM');

      const openaiMessages = messages.map(msg => {
        if (msg.role === 'system') {
          return {
            role: 'system' as const,
            content: msg.content as string
          };
        } else if (msg.role === 'user') {
          if (typeof msg.content === 'string') {
            return {
              role: 'user' as const,
              content: msg.content
            };
          } else {
            // Handle array content (text + images)
            return {
              role: 'user' as const,
              content: msg.content.map(item => {
                if (item.type === 'text') {
                  return { type: 'text' as const, text: item.text || '' };
                } else {
                  return {
                    type: 'image_url' as const,
                    image_url: { url: item.image_url?.url || '' }
                  };
                }
              })
            };
          }
        } else {
          return {
            role: 'assistant' as const,
            content: msg.content as string
          };
        }
      });

      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: openaiMessages,
        temperature: this.config.temperature || 0,
        max_tokens: 4000,
      });

      const choice = response.choices[0];
      if (!choice || !choice.message || !choice.message.content) {
        throw new Error('Invalid response from OpenAI');
      }

      const usage = response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      } : undefined;

      logger.debug(`OpenAI response received, tokens: ${usage?.totalTokens || 'unknown'}`, 'OpenAILLM');

      return {
        content: choice.message.content,
        usage,
      };
    } catch (error) {
      logger.error('OpenAI API request failed', error as Error, 'OpenAILLM');
      throw error;
    }
  }
}
